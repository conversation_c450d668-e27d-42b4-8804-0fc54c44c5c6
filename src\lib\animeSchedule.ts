// 2025年国漫更新时间表数据
// 基于腾讯视频、爱奇艺、B站等平台的更新安排

export interface AnimeItem {
  name: string;
  platform: string;
  time: string;
  status: 'ongoing' | 'completed' | 'upcoming';
  season?: string;
}

export interface WeeklySchedule {
  [key: string]: AnimeItem[];
}

// 2025年国漫更新时间表
export const animeSchedule: WeeklySchedule = {
  '星期一': [
    {
      name: '仙逆',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing',
      season: '第六季'
    },
    {
      name: '我捡起了一地属性',
      platform: '腾讯视频',
      time: '12:00',
      status: 'ongoing'
    }
  ],
  '星期二': [
    {
      name: '吞噬星空',
      platform: '腾讯视频',
      time: '18:00',
      status: 'ongoing',
      season: '第四季'
    },
    {
      name: '长生界',
      platform: '腾讯视频',
      time: '20:00',
      status: 'ongoing'
    }
  ],
  '星期三': [
    {
      name: '遮天',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing'
    },
    {
      name: '一念永恒',
      platform: '腾讯视频',
      time: '12:00',
      status: 'ongoing',
      season: '第四季'
    },
    {
      name: '少年歌行',
      platform: '腾讯视频',
      time: '18:00',
      status: 'ongoing'
    }
  ],
  '星期四': [
    {
      name: '神印王座',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing'
    },
    {
      name: '万界独尊',
      platform: '爱奇艺',
      time: '12:00',
      status: 'ongoing'
    }
  ],
  '星期五': [
    {
      name: '斗罗大陆',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing'
    },
    {
      name: '完美世界',
      platform: '腾讯视频',
      time: '18:00',
      status: 'ongoing'
    }
  ],
  '星期六': [
    {
      name: '斗破苍穹',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing',
      season: '年番'
    },
    {
      name: '神澜奇域无双珠',
      platform: '爱奇艺',
      time: '10:00',
      status: 'ongoing'
    }
  ],
  '星期日': [
    {
      name: '凡人修仙传',
      platform: '腾讯视频',
      time: '10:00',
      status: 'ongoing'
    },
    {
      name: '牧神记',
      platform: 'B站',
      time: '11:00',
      status: 'ongoing'
    },
    {
      name: '万界独尊',
      platform: '爱奇艺',
      time: '12:00',
      status: 'ongoing'
    }
  ]
};

// 获取今天是星期几
export const getTodayWeekday = (): string => {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const today = new Date();
  return days[today.getDay()];
};

// 获取今天的更新列表
export const getTodayAnime = (): AnimeItem[] => {
  const today = getTodayWeekday();
  return animeSchedule[today] || [];
};

// 平台颜色映射
export const platformColors = {
  '腾讯视频': 'bg-blue-500 text-white',
  '爱奇艺': 'bg-green-500 text-white',
  'B站': 'bg-pink-500 text-white',
  '优酷': 'bg-orange-500 text-white'
};
