'use client';

import { Calendar, Clock } from 'lucide-react';
import { useEffect, useState } from 'react';

import { animeSchedule, getTodayWeekday, platformColors, type AnimeItem } from '@/lib/animeSchedule';

const AnimeSchedule = () => {
  const [todayWeekday, setTodayWeekday] = useState('');

  useEffect(() => {
    setTodayWeekday(getTodayWeekday());
  }, []);

  const weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];

  return (
    <div className="w-full bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 mb-8 shadow-lg border border-green-100 dark:border-gray-700">
      {/* 标题区域 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-500 rounded-lg">
            <Calendar className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200">
              国漫更新时间表
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              2025年最新 · 今天是{todayWeekday}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          <Clock className="w-4 h-4" />
          <span>实时更新</span>
        </div>
      </div>

      {/* 时间表网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4">
        {weekdays.map((day) => {
          const isToday = day === todayWeekday;
          const animeList = animeSchedule[day] || [];

          return (
            <div
              key={day}
              className={`relative rounded-lg p-4 transition-all duration-300 ${
                isToday
                  ? 'bg-green-500 text-white shadow-lg scale-105 ring-2 ring-green-300'
                  : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              {/* 今日标识 */}
              {isToday && (
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
                  今日
                </div>
              )}

              {/* 星期标题 */}
              <h4 className={`font-semibold mb-3 ${isToday ? 'text-white' : 'text-gray-800 dark:text-gray-200'}`}>
                {day}
              </h4>

              {/* 动漫列表 */}
              <div className="space-y-2">
                {animeList.length > 0 ? (
                  animeList.map((anime, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded-md transition-all duration-200 ${
                        isToday
                          ? 'bg-white/20 hover:bg-white/30'
                          : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className={`text-sm font-medium truncate ${
                            isToday ? 'text-white' : 'text-gray-800 dark:text-gray-200'
                          }`}>
                            {anime.name}
                          </p>
                          {anime.season && (
                            <p className={`text-xs truncate ${
                              isToday ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'
                            }`}>
                              {anime.season}
                            </p>
                          )}
                        </div>
                        
                        <div className="flex flex-col items-end gap-1">
                          <span className={`text-xs font-mono ${
                            isToday ? 'text-white' : 'text-gray-600 dark:text-gray-300'
                          }`}>
                            {anime.time}
                          </span>
                          <span className={`text-xs px-1.5 py-0.5 rounded text-white ${
                            platformColors[anime.platform as keyof typeof platformColors] || 'bg-gray-500'
                          }`}>
                            {anime.platform}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className={`text-xs text-center py-2 ${
                    isToday ? 'text-white/70' : 'text-gray-400 dark:text-gray-500'
                  }`}>
                    暂无更新
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 底部说明 */}
      <div className="mt-6 pt-4 border-t border-green-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>今日更新</span>
          </div>
          <p>数据更新时间：{new Date().toLocaleDateString('zh-CN')}</p>
        </div>
      </div>
    </div>
  );
};

export default AnimeSchedule;
