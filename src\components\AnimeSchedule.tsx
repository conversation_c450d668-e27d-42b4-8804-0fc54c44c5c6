'use client';

import { Calendar, Clock, Play, Tv } from 'lucide-react';
import { useEffect, useState } from 'react';

import { animeSchedule, getTodayWeekday, platformColors, type AnimeItem } from '@/lib/animeSchedule';

const AnimeSchedule = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [todayWeekday, setTodayWeekday] = useState('');

  // 更新当前时间
  useEffect(() => {
    setTodayWeekday(getTodayWeekday());
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);

  // 检查动漫是否正在更新（当前时间是否在更新时间附近）
  const isCurrentlyUpdating = (anime: AnimeItem): boolean => {
    if (getTodayWeekday() !== todayWeekday) return false;
    
    const [hour, minute] = anime.time.split(':').map(Number);
    const updateTime = new Date();
    updateTime.setHours(hour, minute, 0, 0);
    
    const now = currentTime;
    const timeDiff = Math.abs(now.getTime() - updateTime.getTime());
    
    // 如果在更新时间前后30分钟内，认为正在更新
    return timeDiff <= 30 * 60 * 1000;
  };

  const weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];

  return (
    <div className="w-full bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 mb-8 shadow-lg border border-green-100 dark:border-gray-700">
      {/* 标题区域 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-500 rounded-lg">
            <Calendar className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200">
              国漫更新时间表
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              2025年最新 · 实时更新 · 今天是{todayWeekday}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          <Clock className="w-4 h-4" />
          <span>{currentTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>

      {/* 时间表网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4">
        {weekdays.map((day) => {
          const isToday = day === todayWeekday;
          const animeList = animeSchedule[day] || [];

          return (
            <div
              key={day}
              className={`relative rounded-lg p-4 transition-all duration-300 ${
                isToday
                  ? 'bg-green-500 text-white shadow-lg scale-105 ring-2 ring-green-300'
                  : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              {/* 今日标识 */}
              {isToday && (
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
                  今日
                </div>
              )}

              {/* 星期标题 */}
              <div className="flex items-center gap-2 mb-3">
                <Tv className="w-4 h-4" />
                <h4 className={`font-semibold ${isToday ? 'text-white' : 'text-gray-800 dark:text-gray-200'}`}>
                  {day}
                </h4>
              </div>

              {/* 动漫列表 */}
              <div className="space-y-2">
                {animeList.length > 0 ? (
                  animeList.map((anime, index) => {
                    const isUpdating = isCurrentlyUpdating(anime);
                    
                    return (
                      <div
                        key={index}
                        className={`relative p-2 rounded-md transition-all duration-200 ${
                          isToday
                            ? 'bg-white/20 hover:bg-white/30'
                            : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'
                        }`}
                      >
                        {/* 正在更新指示器 */}
                        {isUpdating && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <p className={`text-sm font-medium truncate ${
                              isToday ? 'text-white' : 'text-gray-800 dark:text-gray-200'
                            }`}>
                              {anime.name}
                            </p>
                            {anime.season && (
                              <p className={`text-xs truncate ${
                                isToday ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'
                              }`}>
                                {anime.season}
                              </p>
                            )}
                          </div>
                          
                          <div className="flex flex-col items-end gap-1">
                            <span className={`text-xs font-mono ${
                              isToday ? 'text-white' : 'text-gray-600 dark:text-gray-300'
                            }`}>
                              {anime.time}
                            </span>
                            <span className={`text-xs px-1.5 py-0.5 rounded text-white ${
                              platformColors[anime.platform as keyof typeof platformColors] || 'bg-gray-500'
                            }`}>
                              {anime.platform}
                            </span>
                          </div>
                        </div>

                        {/* 更新状态指示 */}
                        {isUpdating && (
                          <div className="flex items-center gap-1 mt-1">
                            <Play className="w-3 h-3 text-red-500" />
                            <span className="text-xs text-red-500 font-medium">正在更新</span>
                          </div>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <p className={`text-xs text-center py-2 ${
                    isToday ? 'text-white/70' : 'text-gray-400 dark:text-gray-500'
                  }`}>
                    暂无更新
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 底部说明 */}
      <div className="mt-6 pt-4 border-t border-green-200 dark:border-gray-700">
        <div className="flex flex-wrap items-center justify-between gap-4 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span>正在更新</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>今日</span>
            </div>
          </div>
          <p>数据更新时间：{currentTime.toLocaleDateString('zh-CN')}</p>
        </div>
      </div>
    </div>
  );
};

export default AnimeSchedule;
